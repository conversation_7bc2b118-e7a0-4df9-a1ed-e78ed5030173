ARG BASE_IMAGE=ubuntu:22.04
FROM ${BASE_IMAGE}
LABEL maintainer="<EMAIL>"

# Set timezone and configure non-interactive mode
ENV TZ=Asia/Shanghai
ENV DEBIAN_FRONTEND=noninteractive

# Configure Chinese mirror repositories for faster downloads
RUN ln -sf /usr/share/zoneinfo/${TZ} /etc/localtime && \
    # Configure APT to use Chinese mirrors
    sed -i 's@//.*archive.ubuntu.com@//mirrors.aliyun.com@g' /etc/apt/sources.list && \
    sed -i 's@//.*security.ubuntu.com@//mirrors.aliyun.com@g' /etc/apt/sources.list && \
    # Update package lists and install required system packages
    apt-get update && \
    apt-get install -y \
        python3 \
        python3-dev \
        python3-venv \
        curl \
        ca-certificates \
        build-essential \
        git && \
    # Install uv for Python package management
    curl -LsSf https://astral.sh/uv/install.sh | sh && \
    # Clean up APT cache
    apt-get autoremove -y && \
    apt-get clean -y && \
    rm -rf /var/lib/apt/lists/*

# Add uv to PATH
ENV PATH="/root/.cargo/bin:$PATH"

ARG R2G_REPO=.
ARG R2G_WORKSPACE=/opt/rtl2gds

# Set Python path
ENV PYTHONPATH="${R2G_WORKSPACE}/src"

# Copy project files
COPY ${R2G_REPO}/pyproject.toml ${R2G_WORKSPACE}/
COPY ${R2G_REPO}/uv.lock ${R2G_WORKSPACE}/

# Set working directory
WORKDIR ${R2G_WORKSPACE}

# Install Python dependencies using uv sync
# This will install all dependencies including optional ones defined in pyproject.toml
RUN uv sync --all-extras

# Copy the rest of the application
COPY ${R2G_REPO} ${R2G_WORKSPACE}

# Set the default command
CMD ["/usr/bin/env", "python3", "-m", "rtl2gds", "-h"]
