ARG BASE_IMAGE=ubuntu:22.04
FROM ${BASE_IMAGE}
LABEL maintainer="<EMAIL>"

# Set timezone and configure non-interactive mode
ENV TZ=Asia/Shanghai
ENV DEBIAN_FRONTEND=noninteractive

# Configure Chinese mirror repositories for faster downloads
RUN ln -sf /usr/share/zoneinfo/${TZ} /etc/localtime && \
    # Configure APT to use Chinese mirrors
    sed -i 's@//.*archive.ubuntu.com@//mirrors.aliyun.com@g' /etc/apt/sources.list && \
    sed -i 's@//.*security.ubuntu.com@//mirrors.aliyun.com@g' /etc/apt/sources.list && \
    # Update package lists and install required system packages
    apt-get update && \
    apt-get install -y \
        python3 \
        python3-dev \
        python3-venv \
        curl \
        ca-certificates \
        build-essential \
        git && \
    # Configure Rust/Cargo to use Chinese mirrors for faster uv installation
    mkdir -p /root/.cargo && \
    echo '[source.crates-io]' > /root/.cargo/config.toml && \
    echo 'replace-with = "rsproxy-sparse"' >> /root/.cargo/config.toml && \
    echo '' >> /root/.cargo/config.toml && \
    echo '[source.rsproxy]' >> /root/.cargo/config.toml && \
    echo 'registry = "https://rsproxy.cn/crates.io-index"' >> /root/.cargo/config.toml && \
    echo '' >> /root/.cargo/config.toml && \
    echo '[source.rsproxy-sparse]' >> /root/.cargo/config.toml && \
    echo 'registry = "sparse+https://rsproxy.cn/index/"' >> /root/.cargo/config.toml && \
    echo '' >> /root/.cargo/config.toml && \
    echo '[registries.rsproxy]' >> /root/.cargo/config.toml && \
    echo 'index = "https://rsproxy.cn/crates.io-index"' >> /root/.cargo/config.toml && \
    echo '' >> /root/.cargo/config.toml && \
    echo '[net]' >> /root/.cargo/config.toml && \
    echo 'git-fetch-with-cli = true' >> /root/.cargo/config.toml && \
    # Set Rustup environment variables for Chinese mirrors
    export RUSTUP_DIST_SERVER="https://rsproxy.cn" && \
    export RUSTUP_UPDATE_ROOT="https://rsproxy.cn/rustup" && \
    # Install uv for Python package management using Chinese Rust mirrors
    curl -LsSf https://astral.sh/uv/install.sh | sh && \
    # Clean up APT cache
    apt-get autoremove -y && \
    apt-get clean -y && \
    rm -rf /var/lib/apt/lists/*

# Add uv to PATH
ENV PATH="/root/.local/bin:$PATH"

ARG R2G_REPO=.
ARG R2G_WORKSPACE=/opt/rtl2gds

# Set Python path
ENV PYTHONPATH="${R2G_WORKSPACE}/src"

# Copy project files
COPY ${R2G_REPO}/pyproject.toml ${R2G_WORKSPACE}/
# COPY ${R2G_REPO}/uv.lock ${R2G_WORKSPACE}/

# Set working directory
WORKDIR ${R2G_WORKSPACE}

# Install Python dependencies using uv sync
# This will install all dependencies including optional ones defined in pyproject.toml
RUN uv sync --all-extras

# Copy the rest of the application
COPY ${R2G_REPO} ${R2G_WORKSPACE}

# Set the default command
CMD ["/usr/bin/env", "uv", "run", "-m", "rtl2gds", "-h"]
